import React from 'react';
import NewSchoolModal from './modals/NewSchoolModal';
import BulkSchoolModal from './modals/BulkSchoolModal';
import AssignSchoolModal from './modals/AssignSchoolModal';
import ViewSchoolModal from './modals/ViewSchoolModal';
import EditSchoolModal from './modals/EditSchoolModal';

const SchoolModals = ({ state, actions, onFormChange, schools }) => {
  return (
    <>
      {/* New School Modal */}
      <NewSchoolModal
        open={state.modals.newSchool}
        onClose={actions.handleCloseNewSchool}
        formData={state.forms.newSchool}
        onFormChange={(updates) => onFormChange('newSchool', updates)}
        onSubmit={actions.handleRegisterSchool}
      />

      {/* Bulk School Modal */}
      <BulkSchoolModal
        open={state.modals.bulkSchool}
        onClose={actions.handleCloseBulkSchool}
        selectedFile={state.forms.selectedFile}
        onFileChange={(file) => onFormChange('selectedFile', file)}
        onSubmit={actions.handleBulkRegister}
      />

      {/* Assign School Modal */}
      <AssignSchoolModal
        open={state.modals.assignSchool}
        onClose={actions.handleCloseAssignSchool}
        formData={state.forms.assignData}
        onFormChange={(updates) => onFormChange('assignData', updates)}
        onSubmit={actions.handleAssignSchools}
        schools={schools}
      />

      {/* View School Modal */}
      <ViewSchoolModal
        open={state.modals.viewSchool}
        onClose={actions.handleCloseView}
        schoolData={state.schoolData}
      />

      {/* Edit School Modal */}
      <EditSchoolModal
        open={state.modals.editSchool}
        onClose={actions.handleCloseEdit}
        schoolData={state.schoolData}
        onSubmit={actions.handleUpdateSchool}
      />
    </>
  );
};

export default SchoolModals;
