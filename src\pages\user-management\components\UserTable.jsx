import React from 'react';
import {
  Card,
  Box,
  Typography,
  Button,
  Stack,
  Tabs,
  Tab,
  TextField,
  MenuItem,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton
} from '@mui/material';
import {
  Add,
  SearchFavorite1,
  Edit,
  CloseCircle
} from 'iconsax-react';

const UserTable = ({
  state,
  usersData,
  isLoading,
  onTabChange,
  onTableSearchChange,
  onOpenNewUser,
  onEditUser,
  onDeleteUser
}) => {
  const users = usersData?.content || [];

  return (
    <Card sx={{ borderRadius: 2 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={state.tabValue} onChange={onTabChange} sx={{ px: 2, pt: 1 }}>
          <Tab label="All Users" />
          <Tab label="Active" />
          <Tab label="Inactive" />
        </Tabs>
      </Box>

      <Box
        sx={{
          p: 3,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2
        }}
      >
        <Typography variant="h6">Users List</Typography>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>
          <Button 
            variant="outlined" 
            color="primary" 
            startIcon={<Add />} 
            size="small" 
            onClick={onOpenNewUser}
          >
            Add New User
          </Button>
        </Stack>
      </Box>

      <Box sx={{ px: 3, pb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <TextField 
          select 
          size="small" 
          value={state.rowsPerPage} 
          sx={{ width: 100 }}
        >
          <MenuItem value={5}>5</MenuItem>
          <MenuItem value={10}>10</MenuItem>
          <MenuItem value={25}>25</MenuItem>
          <MenuItem value={50}>50</MenuItem>
        </TextField>
        <TextField
          size="small"
          placeholder="Search..."
          value={state.tableSearchTerm}
          onChange={onTableSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchFavorite1 fontSize="small" />
              </InputAdornment>
            )
          }}
          sx={{ width: { xs: '100%', sm: 250 }, mt: { xs: 2, sm: 0 } }}
        />
      </Box>

      <TableContainer component={Paper} elevation={0} sx={{ border: 'none' }}>
        <Table sx={{ minWidth: 650 }} size="medium">
          <TableHead>
            <TableRow>
              <TableCell>User Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Phone Number</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Assigned School</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.length > 0 ? (
              users.map((row) => (
                <TableRow key={row.id} hover>
                  <TableCell>{row.userName}</TableCell>
                  <TableCell>{row.email}</TableCell>
                  <TableCell>{row.phoneNumber}</TableCell>
                  <TableCell>{row.role}</TableCell>
                  <TableCell>{row.assignedSchool}</TableCell>
                  <TableCell>
                    <Chip
                      label={row.status}
                      color={row.status === 'Active' ? 'success' : 'warning'}
                      size="small"
                      sx={{
                        fontWeight: 500,
                        bgcolor: row.status === 'Active' ? 'success.light' : 'warning.light',
                        color: row.status === 'Active' ? 'success.main' : 'warning.main',
                        border: 'none'
                      }}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <IconButton 
                      size="small" 
                      color="primary"
                      onClick={() => onEditUser(row)}
                    >
                      <Edit fontSize="small" />
                    </IconButton>
                    <IconButton 
                      size="small" 
                      color="error"
                      onClick={() => onDeleteUser(row)}
                    >
                      <CloseCircle fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    {isLoading ? 'Loading users...' : 'No users found matching the current filters'}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Box sx={{ p: 2, borderTop: '1px solid rgba(224, 224, 224, 1)' }}>
        <Typography variant="body2" color="text.secondary">
          Showing{' '}
          {users.length > 0
            ? `1 to ${Math.min(users.length, state.rowsPerPage)} of ${users.length}`
            : '0'}{' '}
          entries
        </Typography>
      </Box>
    </Card>
  );
};

export default UserTable;
