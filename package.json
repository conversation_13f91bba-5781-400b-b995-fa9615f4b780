{"name": "able-pro-material-react", "version": "9.1.2", "private": true, "dependencies": {"@auth0/auth0-spa-js": "^2.0.5", "@date-io/date-fns": "^2.16.0", "@date-io/dayjs": "^2.16.0", "@date-io/luxon": "^2.16.1", "@date-io/moment": "^2.16.1", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@fullcalendar/timeline": "^6.1.8", "@hello-pangea/dnd": "^16.2.0", "@mui/base": "^5.0.0-beta.2", "@mui/icons-material": "^7.1.0", "@mui/lab": "^5.0.0-alpha.131", "@mui/material": "^5.13.2", "@mui/system": "^5.13.2", "@mui/x-date-pickers": "^6.5.0", "@mui/x-date-pickers-pro": "^6.5.0", "@mui/x-tree-view": "^7.28.1", "@react-pdf/renderer": "^3.1.9", "@reduxjs/toolkit": "^1.9.5", "@svgr/webpack": "8.0.1", "@tanstack/react-query": "^5.77.2", "@testing-library/react": "^14.0.0", "amazon-cognito-identity-js": "^6.2.0", "apexcharts": "^3.40.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.4.0", "chance": "^1.1.11", "crypto-js": "^4.1.1", "currency.js": "^2.0.4", "date-fns": "^2.30.0", "dayjs": "^1.11.12", "draft-js": "^0.11.7", "emoji-picker-react": "^4.4.9", "firebase": "^9.22.1", "formik": "^2.2.9", "framer-motion": "^10.12.16", "history": "^5.3.0", "iconsax-react": "^0.0.8", "immutability-helper": "^3.1.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "match-sorter": "^6.3.1", "notistack": "^3.0.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-compare-slider": "^2.2.0", "react-copy-to-clipboard": "^5.1.0", "react-csv": "^2.2.2", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-scrolling": "^1.3.3", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-draggable": "^4.4.5", "react-dropzone": "^14.2.3", "react-fast-marquee": "^1.6.0", "react-google-recaptcha": "^2.1.0", "react-intersection-observer": "^9.4.3", "react-intl": "^6.4.2", "react-number-format": "^5.2.2", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router": "^6.11.2", "react-router-dom": "^6.11.2", "react-scripts": "^5.0.1", "react-slick": "^0.29.0", "react-spring": "9.7.1", "react-syntax-highlighter": "^15.5.0", "react-table": "^7.8.0", "react-table-sticky": "^1.1.3", "react-timer-hook": "^3.0.6", "react-to-print": "^2.14.12", "react-window": "^1.8.9", "react-zoom-pan-pinch": "^3.0.7", "react18-input-otp": "^1.1.3", "redux-persist": "^6.0.0", "rsuite": "^5.68.0", "simplebar": "^6.2.5", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "stylis-plugin-rtl": "2.0.2", "use-places-autocomplete": "^4.0.0", "uuid": "^9.0.0", "web-vitals": "^3.3.1", "yup": "^1.2.0"}, "scripts": {"start": "set PORT=2002 && react-scripts start", "build-stage": "env-cmd -f .env.qa react-scripts build", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/eslint-parser": "^7.21.8", "@babel/plugin-proposal-private-property-in-object": "^7.21.10", "env-cmd": "^10.1.0", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "immutable": "^4.3.0", "prettier": "^2.8.8", "react-error-overlay": "6.0.11"}, "proxy": "https://frogdev.wigal.com.gh"}