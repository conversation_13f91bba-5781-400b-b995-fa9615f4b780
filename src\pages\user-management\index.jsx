/* eslint-disable no-unused-vars */
import '../../assets/datestyle.css';

import React, { useMemo } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { Box, Container } from '@mui/material';

// Project imports
import { theme } from './util';
import { useUserState } from './hooks/useUserState';
import { useUserActions } from './hooks/useUserActions';
import UserStats from './components/UserStats';
import UserFilters from './components/UserFilters';
import UserTable from './components/UserTable';
import UserModals from './components/UserModals';

// Create a custom theme with neutral colors and accent highlights
// const theme = createTheme({
//   palette: {
//     primary: {
//       main: '#546e7a' // Neutral blue-gray as primary
//     },
//     secondary: {
//       main: '#26a69a' // Teal as accent color
//     },
//     background: {
//       default: '#f5f7fa',
//       paper: '#ffffff'
//     },
//     text: {
//       primary: '#37474f',
//       secondary: '#546e7a'
//     }
//   },
//   typography: {
//     fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
//     h4: {
//       fontWeight: 600,
//       fontSize: '1.75rem'
//     },
//     h6: {
//       fontWeight: 600,
//       fontSize: '1.1rem'
//     },
//     subtitle1: {
//       fontSize: '0.9rem',
//       color: '#546e7a'
//     }
//   },
//   shape: {
//     borderRadius: 8
//   },
//   components: {
//     MuiButton: {
//       styleOverrides: {
//         root: {
//           textTransform: 'none',
//           borderRadius: 8,
//           padding: '8px 16px'
//         }
//       }
//     },
//     MuiTableCell: {
//       styleOverrides: {
//         head: {
//           fontWeight: 600,
//           backgroundColor: '#f5f7fa'
//         }
//       }
//     },

//     MuiChip: {
//       styleOverrides: {
//         root: {
//           borderRadius: 6
//         }
//       }
//     }
//   }
// });

// Sample data
const initialWristbands = [
  {
    id: 1,
    userName: 'Fred',
    email: '<EMAIL>',
    phoneNumber: '02445588660',
    role: 'Super Admin',
    assignedSchool: 'N/A',
    status: 'Active'
  },
  {
    id: 2,
    userName: 'Kwame',
    email: '<EMAIL>',
    phoneNumber: '02445588660',
    role: 'Super Admin',
    assignedSchool: 'Accra Academy',
    status: 'Inactive'
  }
];
const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 600,
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 4,
  borderRadius: 2
};

export default function UserManagement() {
  // Use consolidated state management
  const { state, updateState, updateNestedState, toggleModal, updateForm, resetForm } = useUserState();

  // Get users data (placeholder for now - will be replaced with actual API calls)
  const usersData = useMemo(
    () => [
      {
        id: 1,
        userName: 'Fred',
        email: '<EMAIL>',
        phoneNumber: '02445588660',
        role: 'Super Admin',
        assignedSchool: 'N/A',
        status: 'Active'
      },
      {
        id: 2,
        userName: 'Kwame',
        email: '<EMAIL>',
        phoneNumber: '02445588660',
        role: 'Super Admin',
        assignedSchool: 'Accra Academy',
        status: 'Inactive'
      }
    ],
    []
  );

  // Calculate stats from real data
  const { activeCount, inactiveCount } = useMemo(() => {
    if (!usersData) return { activeCount: 0, inactiveCount: 0 };

    const active = usersData.filter((u) => u.status === 'Active').length;
    const inactive = usersData.filter((u) => u.status === 'Inactive').length;

    return { activeCount: active, inactiveCount: inactive };
  }, [usersData]);

  // Use actions hook with real data
  const actions = useUserActions(state, updateState, toggleModal, resetForm, usersData);

  // Form change handler
  const handleFormChange = (formName, updates) => {
    if (formName === 'selectedFile') {
      updateNestedState('forms', { selectedFile: updates });
    } else {
      updateForm(formName, updates);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ bgcolor: 'background.default', minHeight: '100vh', py: 4 }}>
        <Container maxWidth="xl">
          {/* Stats Component */}
          <UserStats activeCount={activeCount} inactiveCount={inactiveCount} />

          {/* Filters Component */}
          <UserFilters
            state={state}
            onToggleFilters={actions.toggleFilters}
            onSearchChange={actions.handleSearchChange}
            onRoleChange={actions.handleRoleChange}
            onStatusChange={actions.handleStatusChange}
          />

          {/* Table Component */}
          <UserTable
            state={state}
            usersData={{ content: usersData, totalElements: usersData?.length || 0 }}
            isLoading={false}
            onTabChange={actions.handleTabChange}
            onPageChange={actions.handleChangePage}
            onRowsPerPageChange={actions.handleChangeRowsPerPage}
            onTableSearchChange={actions.handleTableSearchChange}
            onOpenNewUser={actions.handleOpenNewUser}
            onEditUser={actions.handleOpenEdit}
            onDeleteUser={actions.handleOpenDelete}
          />

          {/* Modals Component */}
          <UserModals state={state} actions={actions} onFormChange={handleFormChange} />
        </Container>
      </Box>
    </ThemeProvider>
  );
}
