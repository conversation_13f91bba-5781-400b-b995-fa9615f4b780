import React from 'react';
import {
  Modal,
  Box,
  Typography,
  IconButton,
  Grid,
  TextField,
  Button
} from '@mui/material';
import { CloseCircle } from 'iconsax-react';
import { modalStyle } from '../../constants/schoolConstants';

const NewSchoolModal = ({ open, onClose, formData, onFormChange, onSubmit }) => {
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onFormChange({ [name]: value });
  };

  const handleSubmit = () => {
    onSubmit();
  };

  const isFormValid = formData.schoolName && formData.schoolNumber && formData.schoolRegion;

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="register-new-school-modal">
      <Box sx={modalStyle}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Register New School
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseCircle fontSize="small" />
          </IconButton>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Register a new school in the TEMVO system.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={6}>
            <Typography variant="subtitle2" gutterBottom>
              School Name
            </Typography>
            <TextField
              fullWidth
              name="schoolName"
              placeholder="Enter School Name"
              value={formData.schoolName}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="subtitle2" gutterBottom>
              School ID
            </Typography>
            <TextField
              fullWidth
              name="schoolNumber"
              placeholder="Enter School ID"
              value={formData.schoolNumber}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="subtitle2" gutterBottom>
              Region
            </Typography>
            <TextField
              fullWidth
              name="schoolRegion"
              placeholder="Enter School's Region"
              value={formData.schoolRegion}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="subtitle2" gutterBottom>
              Admin Name
            </Typography>
            <TextField
              fullWidth
              name="adminName"
              placeholder="Enter School's Admin Name"
              value={formData.adminName}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="subtitle2" gutterBottom>
              Admin Number
            </Typography>
            <TextField
              fullWidth
              name="adminNumber"
              placeholder="Enter Admin's Number"
              value={formData.adminNumber}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="subtitle2" gutterBottom>
              Admin Email
            </Typography>
            <TextField
              fullWidth
              name="adminEmail"
              placeholder="Enter Admin's Email"
              value={formData.adminEmail}
              onChange={handleInputChange}
            />
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4, gap: 2 }}>
          <Button variant="outlined" color="error" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={!isFormValid}
          >
            Register School
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default NewSchoolModal;
